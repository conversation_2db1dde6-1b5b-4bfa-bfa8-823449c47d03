import { axiosInstance } from "@/lib/axios";

export const getMockExamLeaderboard = async (
  timeframe: string,
  page: number = 1,
  limit: number = 10
): Promise<any> => {
  try {
    const response = await axiosInstance.get(
      `/mock-exam-leaderboard/leaderboard/${timeframe}?page=${page}&limit=${limit}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get mock exam leaderboard data: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};

export const getMockExamTopThreeStudents = async (
): Promise<any> => {
  try {
    const response = await axiosInstance.get(
      `/mock-exam-leaderboard/previous-day`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get yesterday's top performers: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};

export const storeReaction = async (
  studentId: string,
  reactionType: string,
  reactorId: string | null
): Promise<any> => {
  try {
    const response = await axiosInstance.post(
      `/reactions`,
      {
        studentId,
        reactionType,
        reactorId
      },
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to send reaction: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};