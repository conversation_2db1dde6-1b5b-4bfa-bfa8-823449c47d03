import { UestCoinTransaction } from "@prisma/client";
import prisma from "@/config/prismaClient";
import { v4 as uuidv4 } from 'uuid';

export const addUestCoinTranscation = async (data: UestCoinTransaction) => {
  try {
    const addUestCoinTranscation = await prisma.uestCoinTransaction.create({
      data
    });
    return addUestCoinTranscation;
  } catch (error) {
    console.error('Error in addUestCoinTranscation:', error);
    throw new Error("Failed to create transaction");
  }
};

export const getUestCoins = async (modelId: string, modelType: string) => {
  try {
    const uestCoins = await prisma.uestCoins.findUnique({
      where: {
        modelId_modelType: {
          modelId,
          modelType,
        },
      },
    });
    return uestCoins;
  } catch (error) {
    console.error('Error in getUestCoins:', error);
    throw new Error("Failed to fetch coins");
  }
};

export const updateOrCreateUestCoins = async (
  modelId: string,
  modelType: string,
  coinsToAdd: number
) => {
  try {
    const existingCoins = await getUestCoins(modelId, modelType);
    
    if (existingCoins) {
      // Update existing coin balance
      const updatedCoins = await prisma.uestCoins.update({
        where: {
          modelId_modelType: {
            modelId,
            modelType,
          },
        },
        data: {
          coins: existingCoins.coins + coinsToAdd,
        },
      });
      return updatedCoins;
    } else {
      const newCoins = await prisma.uestCoins.create({
        data: {
          id: uuidv4(),
          modelId,
          modelType,
          coins: coinsToAdd,
          createdAt: new Date(),
        },
      });
      return newCoins;
    }
  } catch (error) {
    console.error('Error in updateOrCreateUestCoins:', error);
    throw new Error("Failed to update or create coins");
  }
};
