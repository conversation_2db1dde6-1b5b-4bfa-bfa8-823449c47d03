"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import { ChevronDown, ChevronUp, Plus } from "lucide-react";
import { axiosInstance } from "@/lib/axios";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";

interface Transaction {
  id: string;
  type: "CREDIT" | "DEBIT";
  amount: number;
  reason: string;
  createdAt: string;
  modelId?: string;
  modelType?: string;
}

interface CoinsTransactionModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  studentId: string;
}

const CoinsTransactionModal: React.FC<CoinsTransactionModalProps> = ({
  open,
  setOpen,
  studentId,
}) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [coins, setCoins] = useState("");
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchTransactions = async () => {
    setIsLoading(true);
    try {
      console.log("Fetching transactions for studentId:", studentId);
      const response = await axiosInstance.get(
        `/coins/transaction-history/student/${studentId}`
      );
      console.log("Transaction API Response:", response.data);
      setTransactions(response.data.transactions || []);
    } catch (error: any) {
      toast.error(
        `Failed to load transaction history: ${
          error.response?.data?.message || error.message
        }`
      );
      console.error(
        "Error fetching transactions:",
        error.message,
        error.response?.data
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (open && studentId) {
      fetchTransactions();
    }
  }, [open, studentId]);

  const handleAddCoins = async (e: React.FormEvent) => {
    e.preventDefault();
    const amount = parseInt(coins, 10);

    // Validation: must be between 1 and 10000
    if (!amount || amount < 1 || amount > 10000) {
      toast.error("Please enter a number between 1 and 10,000 coins.");
      return;
    }

    setIsSubmitting(true);
    try {
      const payload = {
        studentId,
        amount,
        reason: reason || "Coins added by admin",
      };
      console.log("Sending add coins request:", payload);

      const response = await axiosInstance.post("/coins/add-coins", payload);
      console.log("Add coins response:", response.data);

      toast.success("Coins added successfully.");
      setCoins("");
      setReason("");
      setShowAddForm(false);
      await fetchTransactions();
    } catch (error: any) {
      toast.error(
        `Failed to add coins: ${error.response?.data?.message || error.message}`
      );
      console.error("Error adding coins:", error.message, error.response?.data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const TransactionCard = ({ txn }: { txn: Transaction }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
      <Card
        className="p-4 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="text-base font-semibold text-foreground">
              {txn.type === "CREDIT" ? (
                <span className="text-green-500">Credit</span>
              ) : (
                <span className="text-red-500">Debit</span>
              )}
            </h3>
            <p className="text-sm text-muted-foreground">
              {txn.amount} Coins •{" "}
              {format(new Date(txn.createdAt), "MMM dd, yyyy")}
            </p>
          </div>
          {isExpanded ? (
            <ChevronUp className="h-5 w-5 text-muted-foreground" />
          ) : (
            <ChevronDown className="h-5 w-5 text-muted-foreground" />
          )}
        </div>
        {isExpanded && (
          <div className="mt-3 pt-3 border-t text-sm text-muted-foreground space-y-1 animate-in fade-in">
            <p>
              <strong>Reason:</strong> {txn.reason}
            </p>
            <p>
              <strong>Time:</strong> {format(new Date(txn.createdAt), "p")}
            </p>
            {txn.modelId && (
              <p>
                <strong>Model ID:</strong> {txn.modelId}
              </p>
            )}
            {txn.modelType && (
              <p>
                <strong>Model Type:</strong> {txn.modelType}
              </p>
            )}
          </div>
        )}
      </Card>
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[800px] max-h-[600px] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div>
            <DialogTitle>Transaction History</DialogTitle>
            <DialogDescription className="mt-2">
              View the coin transaction history for the student.
            </DialogDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddForm(!showAddForm)}
            className="flex items-center gap-2 me-5"
          >
            <Plus className="h-4 w-4" />
            Add Coins
          </Button>
        </DialogHeader>
        {showAddForm && (
          <Card className="p-4 mb-4 bg-white rounded-lg shadow-sm border">
            <form onSubmit={handleAddCoins} className="space-y-4">
              <div>
                <Label htmlFor="coins">Number of Coins</Label>
                <Input
                  id="coins"
                  type="number"
                  step="1"
                  value={coins}
                  onChange={(e) => setCoins(e.target.value)}
                  placeholder="Enter number of coins"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="reason">Reason (Optional)</Label>
                <Input
                  id="reason"
                  type="text"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Enter reason for transaction"
                  className="mt-1"
                />
              </div>
              <Button type="submit" disabled={isSubmitting} className="w-full">
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            </form>
          </Card>
        )}
        <div className="mt-4 space-y-4 max-h-[400px] overflow-y-auto">
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full rounded-lg" />
              ))}
            </div>
          ) : transactions.length > 0 ? (
            <div className="grid gap-4">
              {transactions.map((txn) => (
                <TransactionCard key={txn.id} txn={txn} />
              ))}
            </div>
          ) : (
            <Card className="p-8 bg-white rounded-lg shadow-sm border text-center">
              <p className="text-muted-foreground">
                No transactions found for this student.
              </p>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CoinsTransactionModal;
